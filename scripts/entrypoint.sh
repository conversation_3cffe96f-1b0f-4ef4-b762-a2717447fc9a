#!/bin/sh
set -e

SECRET_ENV_FILE="/run/secrets/container_secrets.env"
CONFIG_ENV_FILE="/run/configs/container_configs.env"

load_env_file() {
  local env_file="$1"
  if [ -f "$env_file" ]; then
    echo "Sourcing environment variables from: $env_file"
    set -a
    . "$env_file"
    set +a
  else
    echo "Environment file not found, skipping: $env_file"
  fi
}

load_env_file "$CONFIG_ENV_FILE"
load_env_file "$SECRET_ENV_FILE"

echo "Environment variables sourced. Starting application..."

exec "$@"