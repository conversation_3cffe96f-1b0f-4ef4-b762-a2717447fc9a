workflow:
  name: 'Pipeline for branch: $CI_COMMIT_BRANCH'

stages:
  - test
  - build-and-push
  - pre-deploy
  - deploy

build-fe:
  stage: build-and-push
  image: node:22-alpine
  cache:
    key:
      files:
        - nex-ticket-fe/pnpm-lock.yaml
    paths:
      - ~/.pnpm-store
    policy: pull-push
  script:
    - echo "Starting Nuxt frontend build..."
    - cd nex-ticket-fe
    - echo "Installing corepack..."
    - npm install --global corepack@latest
    - corepack enable
    - echo "Preparing pnpm..."
    - corepack prepare pnpm@latest-10 --activate
    - echo "Installing dependencies with pnpm..."
    - pnpm install --frozen-lockfile
    - echo "Building Nuxt application..."
    - pnpm build
  artifacts:
    paths:
      - nex-ticket-fe/.output/
    expire_in: 1 hour
  rules:
    - if: '$CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "dev"'
      when: on_success
    - when: never

staging-push-fe:
  stage: build-and-push
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
    entrypoint: [""]

  variables:
    GITLAB_IMAGE_TAG: $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_SHORT_SHA
    GITLAB_IMAGE_BRANCH_TAG: $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_REF_SLUG
    BUILD_DOCKERFILE: ./Dockerfile.test.v2
    BUILD_CONTEXT: $CI_PROJECT_DIR/nex-ticket-fe

  needs:
    - job: build-fe
      artifacts: true
  script:
    - echo "Building and pushing image with Kaniko..."
    - /kaniko/executor
      --context "${BUILD_CONTEXT}"
      --dockerfile "./${BUILD_DOCKERFILE}"
      --destination "${GITLAB_IMAGE_TAG}"
      --destination "${GITLAB_IMAGE_BRANCH_TAG}"

  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      when: on_success
    - when: never

staging-push-be:
  stage: build-and-push
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
    entrypoint: [""]
  variables:
    GITLAB_BE_IMAGE_TAG: $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_SHORT_SHA
    GITLAB_BE_IMAGE_BRANCH_TAG: $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_REF_SLUG
    BUILD_BE_DOCKERFILE: ./Dockerfile
    BUILD_BE_CONTEXT: $CI_PROJECT_DIR/nex-ticket-api
  script:
    - echo "Preparing Kaniko auth config..."
    - echo "{\"auths\":{\"$CI_REGISTRY\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$CI_REGISTRY_PASSWORD\"}}}" > /kaniko/.docker/config.json

    - echo "Building and pushing backend image with Kaniko..."
    - /kaniko/executor
      --context "${BUILD_BE_CONTEXT}"
      --dockerfile "${BUILD_BE_DOCKERFILE}"
      --build-arg RAILS_ENV=staging
      --destination "${GITLAB_BE_IMAGE_TAG}"
      --destination "${GITLAB_BE_IMAGE_BRANCH_TAG}"

  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      when: on_success
    - when: never

prod-push-fe:
  stage: build-and-push
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
    entrypoint: [""]

  variables:
    GITLAB_IMAGE_TAG: $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_SHORT_SHA
    GITLAB_IMAGE_BRANCH_TAG: $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_REF_SLUG
    BUILD_DOCKERFILE: ./Dockerfile.test.v2
    BUILD_CONTEXT: $CI_PROJECT_DIR/nex-ticket-fe

  needs:
    - job: build-fe
      artifacts: true
  script:
    - echo "Building and pushing image with Kaniko..."
    - /kaniko/executor
      --context "${BUILD_CONTEXT}"
      --dockerfile "./${BUILD_DOCKERFILE}"
      --destination "${GITLAB_IMAGE_TAG}"
      --destination "${GITLAB_IMAGE_BRANCH_TAG}"

  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
      when: on_success
    - when: never

prod-push-be:
  stage: build-and-push
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
    entrypoint: [""]
  variables:
    GITLAB_BE_IMAGE_TAG: $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_SHORT_SHA
    GITLAB_BE_IMAGE_BRANCH_TAG: $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_REF_SLUG
    BUILD_BE_DOCKERFILE: ./Dockerfile
    BUILD_BE_CONTEXT: $CI_PROJECT_DIR/nex-ticket-api
  script:
    - echo "Preparing Kaniko auth config..."
    - echo "{\"auths\":{\"$CI_REGISTRY\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$CI_REGISTRY_PASSWORD\"}}}" > /kaniko/.docker/config.json

    - echo "Building and pushing backend image with Kaniko..."
    - /kaniko/executor
      --context "${BUILD_BE_CONTEXT}"
      --dockerfile "${BUILD_BE_DOCKERFILE}"
      --build-arg RAILS_ENV=production
      --destination "${GITLAB_BE_IMAGE_TAG}"
      --destination "${GITLAB_BE_IMAGE_BRANCH_TAG}"

  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
      when: on_success
    - when: never

staging-deploy:
  stage: deploy
  environment: staging
  image: alpine/curl:latest
  needs:
    - job: staging-push-be 
    - job: staging-push-fe
  script:
    - echo "Triggering Portainer stack FE update via API..."
    - > 
      curl -H "CF-Access-Client-Id: ${CF_ACCESS_CLIENT_ID}"
      -H "CF-Access-Client-Secret: ${CF_ACCESS_CLIENT_SECRET}"
      -k -X POST
      "${PORTAINER_NUXT_DEPLOY_URL}"
    - echo "Portainer FE API request sent."

    - echo "Triggering Portainer stack BE update via API..."
    - > 
      curl -H "CF-Access-Client-Id: ${CF_ACCESS_CLIENT_ID}"
      -H "CF-Access-Client-Secret: ${CF_ACCESS_CLIENT_SECRET}"
      -k -X POST
      "${PORTAINER_RAILS_DEPLOY_URL}"
    - echo "Portainer BE API request sent."

    - echo "Triggering Portainer stack Sidekiq update via API..."
    - > 
      curl -H "CF-Access-Client-Id: ${CF_ACCESS_CLIENT_ID}"
      -H "CF-Access-Client-Secret: ${CF_ACCESS_CLIENT_SECRET}"
      -k -X POST
      "${PORTAINER_SIDEKIQ_DEPLOY_URL}"
    - echo "Portainer Sidekiq API request sent."
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"' 
      when: on_success
    - when: never

prod-deploy:
  stage: deploy
  environment: production 
  image: alpine/curl:latest
  needs:
    - job: prod-push-be 
    - job: prod-push-fe
  script:
    - echo "Triggering Portainer stack FE update via API..."
    - > 
      curl -H "CF-Access-Client-Id: ${CF_ACCESS_CLIENT_ID}"
      -H "CF-Access-Client-Secret: ${CF_ACCESS_CLIENT_SECRET}"
      -k -X POST
      "${PORTAINER_NUXT_DEPLOY_URL}"
    - echo "Portainer FE API request sent."

    - echo "Triggering Portainer stack BE update via API..."
    - > 
      curl -H "CF-Access-Client-Id: ${CF_ACCESS_CLIENT_ID}"
      -H "CF-Access-Client-Secret: ${CF_ACCESS_CLIENT_SECRET}"
      -k -X POST
      "${PORTAINER_RAILS_DEPLOY_URL}"
    - echo "Portainer BE API request sent."

    - echo "Triggering Portainer stack Sidekiq update via API..."
    - > 
      curl -H "CF-Access-Client-Id: ${CF_ACCESS_CLIENT_ID}"
      -H "CF-Access-Client-Secret: ${CF_ACCESS_CLIENT_SECRET}"
      -k -X POST
      "${PORTAINER_SIDEKIQ_DEPLOY_URL}"
    - echo "Portainer Sidekiq API request sent."
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"' 
      when: on_success
    - when: never
