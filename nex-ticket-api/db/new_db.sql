
CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA extensions;
CREATE EXTENSION IF NOT EXISTS "moddatetime" WITH SCHEMA extensions;

CREATE TYPE user_role AS ENUM ('customer', 'organiser', 'admin');
CREATE TYPE order_status AS ENUM ('pending', 'paid', 'failed', 'refunded', 'cancelled');
CREATE TYPE ticket_generation_status AS ENUM ('pending', 'completed', 'failed');
CREATE TYPE promo_code_strategy AS ENUM ('percentage_total', 'fixed_amount_total');


CREATE TABLE public.profiles (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  user_id UUID UNIQUE NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  first_name TEXT,
  last_name TEXT,
  email TEXT UNIQUE NOT NULL, -- Keep email synced with auth.users for easy access
  role user_role NOT NULL DEFAULT 'customer',
  visitor_token TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (user_id, email, first_name, last_name)
  VALUES (new.id, new.email, new.raw_user_meta_data->>'first_name', new.raw_user_meta_data->>'last_name');
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();


CREATE TABLE public.states (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  name TEXT NOT NULL,
  country_code VARCHAR(2) NOT NULL,
  vat_percentage NUMERIC(5, 2) DEFAULT 0.00
);

CREATE TABLE public.timezones (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  name TEXT UNIQUE NOT NULL,
  offset_str TEXT NOT NULL
);

CREATE TABLE public.cities (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  name TEXT NOT NULL,
  state_id UUID REFERENCES public.states(id) ON DELETE SET NULL,
  timezone_id UUID REFERENCES public.timezones(id) ON DELETE SET NULL,
  basic_relevance INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);


CREATE TABLE public.organisers (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  profile_id UUID UNIQUE NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  website_url TEXT,
  default_currency VARCHAR(3) NOT NULL DEFAULT 'USD',
  platform_fee_percentage NUMERIC(5, 2) DEFAULT 0.00,
  state_id UUID REFERENCES public.states(id),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE public.events (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  organiser_id UUID NOT NULL REFERENCES public.organisers(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  start_time TIMESTAMPTZ NOT NULL,
  end_time TIMESTAMPTZ,
  venue_name TEXT,
  address_line1 TEXT,
  address_line2 TEXT,
  postal_code TEXT,
  city_id UUID REFERENCES public.cities(id) ON DELETE SET NULL,
  currency VARCHAR(3) NOT NULL,
  is_published BOOLEAN DEFAULT false,
  is_disabled BOOLEAN DEFAULT false,
  -- main_image_url and gallery_urls will be managed via Supabase Storage
  created_at TIMESTAMTz NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);


CREATE TABLE public.ticket_types (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  event_id UUID NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  price NUMERIC(10, 2) NOT NULL,
  quantity INTEGER NOT NULL,
  remaining_quantity INTEGER,
  sale_starts_at TIMESTAMPTZ,
  sale_ends_at TIMESTAMPTZ,
  is_disabled BOOLEAN DEFAULT false,
  effective_price NUMERIC(10, 2),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE public.promo_codes (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  organiser_id UUID NOT NULL REFERENCES public.organisers(id) ON DELETE CASCADE,
  code TEXT UNIQUE NOT NULL,
  strategy promo_code_strategy NOT NULL,
  strategy_value NUMERIC(10, 2) NOT NULL,
  max_uses INTEGER,
  uses_count INTEGER DEFAULT 0,
  valid_from TIMESTAMPTZ,
  valid_until TIMESTAMPTZ,
  is_disabled BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);


CREATE TABLE public.baskets (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  visitor_token TEXT,
  expires_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  CONSTRAINT user_or_visitor CHECK (profile_id IS NOT NULL OR visitor_token IS NOT NULL)
);

CREATE TABLE public.basket_items (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  basket_id UUID NOT NULL REFERENCES public.baskets(id) ON DELETE CASCADE,
  ticket_type_id UUID NOT NULL REFERENCES public.ticket_types(id) ON DELETE CASCADE,
  quantity INTEGER NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE public.orders (
  id BIGINT PRIMARY KEY, -- Using BIGINT to match your previous sequence-based approach
  profile_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
  status order_status NOT NULL DEFAULT 'pending',
  ticket_generation_status ticket_generation_status NOT NULL DEFAULT 'pending',
  customer_email TEXT,
  customer_first_name TEXT,
  customer_last_name TEXT,
  subtotal_amount NUMERIC(10, 2),
  vat_amount NUMERIC(10, 2),
  promo_code_id UUID REFERENCES public.promo_codes(id),
  promo_discount_amount NUMERIC(10, 2),
  total_amount NUMERIC(10, 2),
  currency_code VARCHAR(3),
  stripe_payment_intent_id TEXT,
  agreed_to_terms BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE SEQUENCE public.order_id_seq
  START WITH 10000
  INCREMENT BY 1
  NO MINVALUE
  NO MAXVALUE
  CACHE 1;

ALTER TABLE public.orders ALTER COLUMN id SET DEFAULT nextval('public.order_id_seq');


CREATE TABLE public.order_items (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  order_id BIGINT NOT NULL REFERENCES public.orders(id) ON DELETE CASCADE,
  ticket_type_id UUID NOT NULL REFERENCES public.ticket_types(id) ON DELETE RESTRICT,
  quantity INTEGER NOT NULL,
  unit_price NUMERIC(10, 2) NOT NULL,
  platform_fee NUMERIC(10, 2),
  total_price NUMERIC(10, 2) NOT NULL
);

CREATE TABLE public.tickets (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  order_item_id UUID NOT NULL REFERENCES public.order_items(id) ON DELETE CASCADE,
  qr_code_url TEXT, -- This could be a reference to a file in Supabase Storage
  is_used BOOLEAN DEFAULT false,
  used_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);


CREATE TABLE public.tag_categories (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  name TEXT UNIQUE NOT NULL
);

CREATE TABLE public.tags (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  name TEXT UNIQUE NOT NULL
);

CREATE TABLE public.events_tags (
  event_id UUID NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
  tag_id UUID NOT NULL REFERENCES public.tags(id) ON DELETE CASCADE,
  PRIMARY KEY (event_id, tag_id)
);

CREATE TABLE public.tag_categories_tags (
  tag_category_id UUID NOT NULL REFERENCES public.tag_categories(id) ON DELETE CASCADE,
  tag_id UUID NOT NULL REFERENCES public.tags(id) ON DELETE CASCADE,
  PRIMARY KEY (tag_category_id, tag_id)
);


CREATE TABLE public.saved_events (
  profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  event_id UUID NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  PRIMARY KEY (profile_id, event_id)
);

CREATE TABLE public.mail_subscriptions (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);


CREATE INDEX ON public.profiles (user_id);
CREATE INDEX ON public.organisers (profile_id);
CREATE INDEX ON public.events (organiser_id);
CREATE INDEX ON public.events (city_id);
CREATE INDEX ON public.ticket_types (event_id);
CREATE INDEX ON public.baskets (profile_id);
CREATE INDEX ON public.orders (profile_id);
CREATE INDEX ON public.saved_events (profile_id, event_id);

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.profiles
  FOR EACH ROW EXECUTE PROCEDURE moddatetime (updated_at);

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.cities
  FOR EACH ROW EXECUTE PROCEDURE moddatetime (updated_at);

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.organisers
  FOR EACH ROW EXECUTE PROCEDURE moddatetime (updated_at);

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.events
  FOR EACH ROW EXECUTE PROCEDURE moddatetime (updated_at);

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.ticket_types
  FOR EACH ROW EXECUTE PROCEDURE moddatetime (updated_at);

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.promo_codes
  FOR EACH ROW EXECUTE PROCEDURE moddatetime (updated_at);

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.baskets
  FOR EACH ROW EXECUTE PROCEDURE moddatetime (updated_at);

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.basket_items
  FOR EACH ROW EXECUTE PROCEDURE moddatetime (updated_at);

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.orders
  FOR EACH ROW EXECUTE PROCEDURE moddatetime (updated_at);

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.tickets
  FOR EACH ROW EXECUTE PROCEDURE moddatetime (updated_at);
