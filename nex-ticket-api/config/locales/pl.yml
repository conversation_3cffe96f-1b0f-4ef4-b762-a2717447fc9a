pl:
  hello: "<PERSON><PERSON><PERSON> świ<PERSON><PERSON>"

  controllers:
    errors:
      no_organiser: "Nie znaleziono powiązanego organizatora"
      unauthorized: "Nieautoryzowany dostęp"
      cannot_delete_event: "Nie można usunąć wydarzenia z istniejącymi zamówieniami."
      event_destroy_error: "Wydarzenie nie mogło zostać usunięte: %{message}"
      promo_code_not_found: "Kod promocyjny nie został znaleziony."
      unauthorized_organiser: "Nieautoryzowany dostęp. Wymagana rola organizatora."
      promo_code_used: "Nie można usunąć kodu promocyjnego, który został u<PERSON> (%{count}). R<PERSON><PERSON>ż jego dezaktywację."
      promo_code_destroy_error: "Kod promocyjny nie mógł zostać usunięty."
      organiser_required: "Wymagany kontekst organizatora."
      promo_code_cant_be_blank: "Kod promocyjny nie może by<PERSON> pusty."
      promo_code_error_unexpected_apply: "Wystąpił nieoczekiwany błąd podczas stosowania kodu promocyjnego."
      promo_code_error_unexpected_remove: "Wystąpił nieoczekiwany błąd podczas usuwania kodu promocyjnego."
      no_order_found: "Zamówienie nie zostało znalezione."
      record_not_found: "Żądany zasób nie został znaleziony."
      record_not_destroyed: "Zasób nie mógł zostać usunięty."
      foreign_key_constraint: "Nie można usunąć tego zasobu, ponieważ jest używany przez inne dane."
      parameter_missing: "Wymagany parametr '%{parameter}' jest brakujący."
      invalid_argument: "Podano nieprawidłowy argument."
      internal_server_error: "Wystąpił nieoczekiwany błąd. Spróbuj ponownie później."

  auth:
    invalid_credentials: "Błąd: Nieprawidłowa nazwa użytkownika lub hasło. Spróbuj ponownie."
    login_success: "Zalogowano pomyślnie."
    logout_success: "Wylogowano pomyślnie"
    no_active_session: "Nie można znaleźć aktywnej sesji."
    signup_success: "Zarejestrowano pomyślnie."
    user_create_error: "Użytkownik nie mógł zostać utworzony pomyślnie. %{errors}"

  models:
    event:
      end_time_error: "musi być po czasie rozpoczęcia"
      invalid_tag_ids: "zawiera nieprawidłowe ID tagów: %{ids}"
      social_media_format: "musi być tablicą obiektów JSON z 'platform' i 'link'"
      policies_format: "musi być tablicą obiektów JSON z 'type' i 'details'"

    promo_code:
      max_uses_below_current: "nie można ustawić poniżej bieżącego użycia (%{count})"
      valid_until_in_past: "nie można ustawić w przeszłości"
      invalid_event_ids: "zawierają nieprawidłowe lub nieautoryzowane ID: %{ids}"
      invalid_ticket_type_ids: "zawierają nieprawidłowe lub nieautoryzowane ID: %{ids}"
      valid_until_before_from: "musi być po valid_from"
      discount_value_exceeds_100: "nie może przekroczyć 100% dla zniżek procentowych"
      event_ids_not_array: "musi być tablicą"
      ticket_type_ids_not_array: "musi być tablicą"

  model:
    errors:
      promo_code_processing: "Zamówienie musi być w stanie przetwarzania, aby zastosować kod promocyjny."
      promo_code_not_found: "Kod promocyjny nie został znaleziony lub nie jest dostępny."
      promo_code_n_a: "Kod promocyjny nie ma zastosowania do tego zamówienia."
      promo_code_save_fail: "Nie udało się zapisać zamówienia z kodem promocyjnym: %{message}"
      promo_code_apply_fail: "Nie udało się zastosować kodu promocyjnego: %{message}"
      promo_code_remove_fail: "Zamówienie musi być w stanie przetwarzania, aby usunąć kod promocyjny."
      no_promo_code_applied: "Żaden kod promocyjny nie jest obecnie zastosowany do tego zamówienia."
      promo_code_order_save_fail: "Nie udało się zapisać zamówienia po usunięciu kodu promocyjnego: %{message}"
      can_not_remove_promo_code: "Nie można usunąć kodu promocyjnego: %{message}"
      total_cant_be_negative: "Suma zamówienia nie może być ujemna lub zerowa."
      profile_picture_invalid: "Zdjęcie profilowe jest nieprawidłowe lub nie masz uprawnień."

  contact:
    email_sent_success: "Email został wysłany pomyślnie"
    missing_required_parameters: "Brakuje wymaganych parametrów"

  mailer:
    welcome:
      subject: "Rejestracja pomyślna!"
      greeting: "Cześć %{name},"
      success_message: "Pomyślnie zarejestrowałeś się w TicketPie!"
      excitement_message: "Cieszymy się, że do nas dołączyłeś. Teraz możesz przeglądać i kupować bilety na swoje ulubione wydarzenia."
      best_regards: "Z poważaniem,"
      team_name: "Zespół TicketPie"
      contact_us: "Skontaktuj się z nami na"

  errors:
    insufficient_available_amount: "Niewystarczająca dostępna ilość"
