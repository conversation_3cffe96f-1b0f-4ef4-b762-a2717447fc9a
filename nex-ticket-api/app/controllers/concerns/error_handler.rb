module ErrorHandler
  extend ActiveSupport::Concern

  included do
    # Handle common Rails exceptions
    rescue_from ActiveRecord::RecordNotFound, with: :handle_record_not_found
    rescue_from ActiveRecord::RecordInvalid, with: :handle_record_invalid
    rescue_from ActiveRecord::RecordNotDestroyed, with: :handle_record_not_destroyed
    rescue_from ActiveRecord::InvalidForeignKey, with: :handle_foreign_key_constraint
    
    # Handle authorization exceptions
    rescue_from CanCan::AccessDenied, with: :handle_access_denied
    
    # Handle custom application exceptions
    rescue_from InsufficientAvailableAmountError, with: :handle_insufficient_available_amount
    rescue_from Order::PromoCodeError, with: :handle_promo_code_error
    
    # Handle parameter errors
    rescue_from ActionController::ParameterMissing, with: :handle_parameter_missing
    rescue_from ArgumentError, with: :handle_argument_error
    
    # Handle unexpected errors in production
    rescue_from StandardError, with: :handle_standard_error unless Rails.env.development?
  end

  private

  # Centralized method for rendering API errors
  # Always returns errors in array format for consistency
  def render_api_error(message_or_messages, status = :unprocessable_entity, log_error: false)
    messages = Array(message_or_messages).flatten.compact
    
    # Translate messages if they are translation keys
    translated_messages = messages.map do |msg|
      if msg.is_a?(String) && msg.include?('.')
        I18n.t(msg, default: msg)
      else
        msg.to_s
      end
    end

    # Log error if requested
    if log_error
      Rails.logger.error("API Error: #{translated_messages.join(', ')} - Status: #{status}")
    end

    render json: { errors: translated_messages }, status: status
  end

  # Handle ActiveRecord::RecordNotFound
  def handle_record_not_found(exception)
    render_api_error(I18n.t('controllers.errors.record_not_found'), :not_found)
  end

  # Handle ActiveRecord::RecordInvalid (validation errors)
  def handle_record_invalid(exception)
    render_api_error(exception.record.errors.full_messages, :unprocessable_entity)
  end

  # Handle ActiveRecord::RecordNotDestroyed
  def handle_record_not_destroyed(exception)
    messages = exception.record.errors.full_messages
    messages = [I18n.t('controllers.errors.record_not_destroyed')] if messages.empty?
    render_api_error(messages, :unprocessable_entity)
  end

  # Handle foreign key constraint violations
  def handle_foreign_key_constraint(exception)
    render_api_error(I18n.t('controllers.errors.foreign_key_constraint'), :conflict)
  end

  # Handle CanCan::AccessDenied
  def handle_access_denied(exception)
    render_api_error(I18n.t('controllers.errors.unauthorized'), :forbidden)
  end

  # Handle InsufficientAvailableAmountError
  def handle_insufficient_available_amount(exception)
    render_api_error(exception.message, :unprocessable_entity)
  end

  # Handle Order::PromoCodeError
  def handle_promo_code_error(exception)
    render_api_error(exception.message, :unprocessable_entity)
  end

  # Handle ActionController::ParameterMissing
  def handle_parameter_missing(exception)
    message = I18n.t('controllers.errors.parameter_missing', parameter: exception.param)
    render_api_error(message, :bad_request)
  end

  # Handle ArgumentError
  def handle_argument_error(exception)
    render_api_error(I18n.t('controllers.errors.invalid_argument'), :bad_request, log_error: true)
  end

  # Handle unexpected StandardError (only in production)
  def handle_standard_error(exception)
    # Log the full error for debugging
    Rails.logger.error("Unexpected error: #{exception.class} - #{exception.message}")
    Rails.logger.error(exception.backtrace.join("\n"))
    
    # Send to error tracking service (Rollbar)
    Rollbar.error(exception) if defined?(Rollbar)
    
    # Return generic error message to user
    render_api_error(I18n.t('controllers.errors.internal_server_error'), :internal_server_error)
  end

  # Legacy method for backward compatibility - delegates to render_api_error
  def render_error(resource_or_message, status = :unprocessable_entity)
    if resource_or_message.respond_to?(:errors)
      render_api_error(resource_or_message.errors.full_messages, status)
    else
      render_api_error(resource_or_message, status)
    end
  end
end
