module Public
  class SavedEventsController < ApplicationController
    include Customerable

    # POST /saved_event
    def save_event
      customer = find_or_create_customer
      saved_event = SavedEvent.find_by(event_id: params[:event_id], customer: customer)

      if saved_event
        render_api_error("Event already saved for this customer", :unprocessable_entity)
      else
        saved_event = SavedEvent.new(event_id: params[:event_id], customer: customer)
        if saved_event.save
          response.headers["Visitor-Token"] =
request.headers["Visitor-Token"] if request.headers["Visitor-Token"].present?
          render json: { message: "Event saved successfully" }, status: :ok
        else
          render_api_error(saved_event.errors.full_messages, :unprocessable_entity)
        end
      end
    end

    # GET /saved_events
    def index
      customer = find_or_create_customer
      saved_events = customer.saved_events

      render json: saved_events, status: :ok
    end

    # DELETE /saved_event/:id
    def destroy
      customer = find_customer
      saved_event = SavedEvent.find_by(event_id: params[:id], customer: customer)

      if saved_event
        saved_event.destroy
        render json: { message: "Event deleted successfully" }, status: :ok
      else
        render_api_error("Event not found", :not_found)
      end
    end

    private

      # Only allow a list of trusted parameters through.
      def event_params
        params.require(:event).permit(:event_id)
      end
  end
end
