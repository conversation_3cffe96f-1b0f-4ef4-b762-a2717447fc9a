module Public
  class TicketTypesController < ApplicationController
    # GET /ticket_types
    def index
      if params[:event_id].present?
        @ticket_types = TicketType.where(event_id: params[:event_id])
        render json: TicketTypeSerializer.new(@ticket_types).serializable_hash
      else
        render_api_error("event_id parameter is required", :unprocessable_entity)
      end
    end
  end
end
