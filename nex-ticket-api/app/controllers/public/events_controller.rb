module Public
  class EventsController < ApplicationController
    include Paginationable
    include Customerable

    ALLOWED_INCLUDES = %w[ticket_types organiser tags].freeze

    before_action -> { filter_and_set_includes params[:includes] }
    before_action :set_event, only: %i[ show ]

    # GET /api/public/events
    def index
      events = Event.includes(@includes)

      events = events.by_city(params[:city]) if params[:city].present?
      events = events.by_min_date(params[:min_date]) if params[:min_date].present?
      events = events.by_max_date(params[:max_date]) if params[:max_date].present?
      events = events.by_min_discounted_price(params[:min_price]) if params[:min_price].present?
      events = events.by_max_discounted_price(params[:max_price]) if params[:max_price].present?
      events = events.where(disabled: false)
      if params[:tags].present?
        begin
          tag_ids = JSON.parse(params[:tags])
          events = events.by_tags(tag_ids) if tag_ids.is_a?(Array)
        rescue JSON::ParserError => e
          Rails.logger.warn "Could not parse tags parameter: #{params[:tags]}. Error: #{e.message}"
          render_api_error("Invalid tags format", :bad_request) and return
        end
      end


      if params[:price_sort_direction].present?
        direction = params[:price_sort_direction].downcase == "asc" ? "ASC" : "DESC"
        events = events.joins(:ticket_types).select("events.*, MIN(ticket_types.price - (ticket_types.price * (COALESCE(ticket_types.discounts->>'percentage', '0')::float / 100))) AS min_discounted_price")
                       .group("events.id")
                       .order("min_discounted_price #{direction}")
                       .references(:ticket_types)
      end

      if params[:date_sort_direction].present?
        direction = params[:date_sort_direction].downcase == "asc" ? :asc : :desc
        events = events.order(start_time: direction)
      end

      # Apply pagination if needed (uncomment and adjust as necessary)
      # per_page = params[:per_page] || 10 # Default per page
      # page = params[:page] || 1 # Default page
      # paginate_offset = (page.to_i - 1) * per_page.to_i
      # events = events.limit(per_page).offset(paginate_offset)

      render json: EventSerializer.new(events, { include: @includes, params: { current_user: current_customer } }).serializable_hash,
status: :ok
    end

    # GET /events/1
    def show
      filtered_ticket_types = filter_active_ticket_types(@event)
      render json: EventSerializer.new(@event, include: @includes, params: { current_user: current_customer, ticket_types: filtered_ticket_types }).serializable_hash,
status: :ok
    end

    private
      def filter_and_set_includes(includes_param)
        requested_includes = includes_param.to_s.split(",").map(&:strip)
        @includes = requested_includes.select { |association| ALLOWED_INCLUDES.include?(association) }
      end

      def set_event
        @event = Event.includes(@includes).find(params[:id])
      end

      def event_params
        params.require(:event).permit(*EVENT_PARAMS)
      end

      def event_params_with_includes
        params.require(:event).permit(*EVENT_PARAMS_WITH_INCLUDES)
      end

      def filter_active_ticket_types(event)
        event.ticket_types.where(disabled: false).order(price: :asc)
      end
  end
end
