module Public
  class BasketsController < ApplicationController
    include Customerable
    before_action :set_basket

    def show
      render json: BasketSerializer.new(@basket, include: [ :basket_items ]).serializable_hash
    end

    def remove_item
      basket_item = @basket.basket_items.find_by(id: params[:id])
      if basket_item && basket_item.basket == @basket
        basket_item.destroy
        head :no_content
      else
        render_api_error("Item not found in basket or does not belong to the current user/visitor", :not_found)
      end
    end

    def update_item
      basket_item = @basket.basket_items.find_or_initialize_by(itemable_type: basket_params[:itemable_type],
itemable_id: basket_params[:itemable_id])
      if params[:quantity].to_i <= 0
        basket_item.destroy
        head :no_content
      else
        basket_item.quantity = params[:quantity]
        if basket_item.save
          render json: basket_item
        else
          render_api_error(basket_item.errors.full_messages, :unprocessable_entity)
        end
      end
    end

    private

      def set_basket
        customer = find_or_create_customer
        @basket = Basket.find_or_create_by(customer: customer)
        @basket.renew_lifetime if @basket.persisted?
      end

      def basket_params
        params.permit(*BASKET_PARAMS)
      end
  end
end
