# app/controllers/organisers/promo_codes_controller.rb
module Organisers
  class PromoCodesController < ApplicationController
    ALLOWED_INCLUDES = %w[organiser orders].freeze

    before_action :filter_and_set_includes
    before_action :authenticate_user!
    before_action :set_promo_code, only: %i[show update destroy]
    before_action :authorize_organiser_action!, only: %i[create]
    before_action :authorize_access!, only: %i[show update destroy]

    def index
      if current_user.is_admin?
        promo_codes = PromoCode.includes(@includes).order(created_at: :desc)
      elsif current_organiser.present?
        promo_codes = current_organiser.promo_codes.includes(@includes).order(created_at: :desc)
      else
        return render_error(I18n.t("controllers.errors.organiser_required"), :unauthorized)
      end
      render_promo_code_json(promo_codes)
    end

    def show
      render_promo_code_json(@promo_code)
    end

    def create
      @promo_code = current_organiser.promo_codes.build(promo_code_create_params)

      if @promo_code.save
        render_promo_code_json(@promo_code, :created)
      else
        render_validation_errors(@promo_code)
      end
    end

    def update
      if @promo_code.update(promo_code_update_params)
        render_promo_code_json(@promo_code)
      else
        render_validation_errors(@promo_code)
      end
    end

    def destroy
      if @promo_code.uses_count > 0
        render_error(
I18n.t("controllers.errors.promo_code_used", count: ActionController::Base.helpers.pluralize(@promo_code.uses_count,
"time")), :unprocessable_entity)
        return
      end

      if @promo_code.destroy
        head :no_content
      else
        render_error(I18n.t("controllers.errors.promo_code_destroy_error"), :unprocessable_entity)
      end
    end

    private

      def filter_and_set_includes
        requested_includes = params[:includes].to_s.split(",")
        @includes = requested_includes.select { |association| ALLOWED_INCLUDES.include?(association) }
      end

      def set_promo_code
        @promo_code = PromoCode.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        render_error(I18n.t("controllers.errors.promo_code_not_found"), :not_found)
      end

      def authorize_organiser_action!
        render_error(I18n.t("controllers.errors.unauthorized_organiser"),
  :forbidden) unless current_user.is_organiser? && current_organiser.present?
      end

      def authorize_access!
        @promo_code = PromoCode.includes(@includes).find(params[:id]) if @includes.any? && !@promo_code

        unless current_user.is_admin? || (current_organiser.present? && @promo_code.organiser_id == current_organiser.id)
          render_error(I18n.t("controllers.errors.unauthorized"), :forbidden)
        end
      rescue ActiveRecord::RecordNotFound
        render_error(I18n.t("controllers.errors.promo_code_not_found"), :not_found)
      end

      def current_organiser
        @current_organiser ||= current_user&.organiser
      end

      def promo_code_create_params
        params.require(:promo_code).permit(
          :code,
          :description,
          :discount_type,
          :discount_value,
          :valid_from,
          :valid_until,
          :max_uses,
          :max_uses_per_user,
          :min_order_amount,
          :active,
          applicable_event_ids: [],
          applicable_ticket_type_ids: []
        )
      end

      def promo_code_update_params
        params.require(:promo_code).permit(
          :description,
          :valid_from,
          :valid_until,
          :max_uses,
          :max_uses_per_user,
          :min_order_amount,
          :active,
          applicable_event_ids: [],
          applicable_ticket_type_ids: []
        )
      end

      def render_promo_code_json(resource, status = :ok)
        render json: PromoCodeSerializer.new(resource, { include: @includes, params: serializer_params }).serializable_hash,
  status: status
      end

      def render_validation_errors(resource)
        render_api_error(resource.errors.full_messages, :unprocessable_entity)
      end

      def render_error(message, status = :unprocessable_entity)
        render_api_error(message, status)
      end

      def serializer_params
        { current_user: current_user }
      end
  end
end
