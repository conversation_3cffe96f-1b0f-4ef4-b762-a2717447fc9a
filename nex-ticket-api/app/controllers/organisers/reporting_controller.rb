module Organisers
  class ReportingController < ApplicationController
    before_action :authenticate_organiser!

    def event
      event = Event.find(params[:id])
      include_pending = params[:include_pending].presence || false
      start_date, end_date = extract_dates(params)
      stats = calculate_event_stats(event, include_pending, start_date, end_date)
      render json: stats
    end

    def organiser
      return render_unauthorized unless current_user&.is_organiser?

      begin
        start_date, end_date = extract_dates(params)
        include_pending = params[:include_pending].presence || false
      rescue ArgumentError
        return render_api_error("Invalid date format", :bad_request)
      end

      organiser = current_user.organiser
      stats = calculate_organiser_stats(organiser, start_date, end_date, include_pending)

      render json: stats
    end

    private

      def calculate_event_stats(event, include_pending, start_date, end_date)
        order_items = OrderItem.where(
          order_itemable_type: "TicketType",
          order_itemable_id: event.ticket_types.select(:id)
        ).joins(:order)

        order_items = filter_order_items_by_status(order_items, include_pending)
        order_items = filter_order_items_by_date(order_items, start_date, end_date)

        calculate_stats(order_items, event.organiser, :event, start_date, end_date)
      end

      def calculate_organiser_stats(organiser, start_date = nil, end_date = nil, include_pending)
        order_items = OrderItem.joins("INNER JOIN ticket_types AS tt ON order_items.order_itemable_id = tt.id AND order_items.order_itemable_type = 'TicketType'")
                               .joins("INNER JOIN events AS e ON tt.event_id = e.id")
                               .joins(:order)
                               .where(e: { organiser_id: organiser.id })

        order_items = filter_order_items_by_status(order_items, include_pending)
        order_items = filter_order_items_by_date(order_items, start_date, end_date)

        calculate_stats(order_items, organiser, :organiser, start_date, end_date)
      end

      def filter_order_items_by_status(order_items, include_pending)
        if include_pending
          order_items.where(orders: { status: [ :success, :processing ] })
        else
          order_items.where(orders: { status: :successs })
        end
      end

      def filter_order_items_by_date(order_items, start_date, end_date)
        if start_date && end_date
          order_items.where(orders: { created_at: start_date..end_date.end_of_day })
        elsif start_date
          order_items.where(orders: { created_at: start_date.. })
        elsif end_date
          order_items.where(orders: { created_at: ..end_date.end_of_day })
        else
          order_items
        end
      end

      def extract_dates(params)
        start_date = params[:start_date].presence&.to_date
        end_date = params[:end_date].presence&.to_date
        [ start_date, end_date ]
      end

      def calculate_stats(order_items, organiser, context, start_date = nil, end_date = nil)
        total_gains = order_items.sum("organiser_price_per_piece * quantity").to_f
        total_tickets_sold = order_items.sum(:quantity)

        first_sale_date = order_items.minimum("orders.created_at")&.to_date
        last_sale_date = order_items.maximum("orders.created_at")&.to_date

        start_date ||= first_sale_date
        end_date ||= last_sale_date

        if start_date.nil? || end_date.nil?
          no_data_stats = {
            total_revenue: 0.0,
            daily_sales: []
          }

          if context == :organiser
            no_data_stats[:events_breakdown] = []
          elsif context == :event
            no_data_stats.merge!(
              tickettype_breakdown: [],
              total_tickets_sold: 0,
              daily_sales_per_ticket_type: []
            )
          end
          return no_data_stats
        end

        date_range = (start_date..end_date).to_a

        daily_sales = order_items.joins(:order)
                                 .group("DATE(orders.created_at AT TIME ZONE 'UTC' AT TIME ZONE 'CET')")
                                 .sum("quantity * organiser_price_per_piece")
                                 .map { |date, total| { date: date, total: total.round(2) } }
                                 .sort_by { |entry| entry[:date] }

        daily_sales = date_range.map do |date|
          daily_sales.find { |entry| entry[:date] == date } || { date: date, total: 0.0 }
        end

        stats = {
          total_revenue: total_gains.round(2),
          daily_sales: daily_sales
        }

        if context == :organiser
          stats[:events_breakdown] = order_items.joins("INNER JOIN ticket_types AS tt ON order_items.order_itemable_id = tt.id AND order_items.order_itemable_type = 'TicketType'")
                                                .joins("INNER JOIN events AS e ON tt.event_id = e.id")
                                                .group("e.id", "e.name")
                                                .sum("quantity * organiser_price_per_piece")
                                                .map { |(id, name), total|
   { id: id, name: name, total: total.round(2) } }
        elsif context == :event
          stats.merge!(
            tickettype_breakdown: order_items.joins("INNER JOIN ticket_types AS tt ON order_items.order_itemable_id = tt.id AND order_items.order_itemable_type = 'TicketType'")
                                             .group("tt.id", "tt.name")
                                             .select("tt.id, tt.name, SUM(quantity * organiser_price_per_piece) AS total, SUM(quantity) AS tickets_sold")
                                             .map { |tt|
   { id: tt.id, name: tt.name, total: tt.total.round(2),
  tickets_sold: tt.tickets_sold } },
            total_tickets_sold: total_tickets_sold,
            daily_sales_per_ticket_type: order_items.joins("INNER JOIN ticket_types AS tt ON order_items.order_itemable_id = tt.id AND order_items.order_itemable_type = 'TicketType'")
                                                    .joins(:order)
                                                    .group("tt.id", "tt.name", "DATE(orders.created_at AT TIME ZONE 'UTC' AT TIME ZONE 'CET')")
                                                    .sum("quantity * organiser_price_per_piece")
                                                    .map { |(id, name, date), total|
   { id: id, name: name, date: date,
  total: total.round(2) } }
                                                    .sort_by { |entry|
   entry[:date] }
          )
        end

        stats
      end

      def render_unauthorized
        render_api_error("Unauthorized", :unauthorized)
      end
  end
end
