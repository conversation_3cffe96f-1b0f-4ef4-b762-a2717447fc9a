module Organisers
  class ImagesController < ApplicationController
    before_action :authenticate_user!
    before_action :authenticate_organiser!

    def upload
      file = params[:file]
      if file.present?
        blob = ActiveStorage::Blob.create_and_upload!(
          io: file,
          filename: file.original_filename,
          content_type: file.content_type,
          identify: false,
          metadata: { uploaded_by: { user_id: current_user.id, organiser_id: current_user.organiser_id } }
        )

        if blob.image?
          image_url = Rails.application.routes.url_helpers.rails_blob_url(blob, host: request.base_url,
script_name: "/api")
          render json: { message: "Image uploaded successfully", image_key: blob.key, image_url: image_url }
        else
          blob.purge
          render_api_error("File is not an image", :unprocessable_entity)
        end
      else
        render_api_error("No file provided", :bad_request)
      end
    end

    def delete
      attachment = ActiveStorage::Attachment
                     .joins(:blob)
                     .find_by(active_storage_blobs: { key: params[:key] })
      if attachment.present?
        if attachment.blob.metadata["uploaded_by"]["organiser_id"] == current_user.organiser_id
          attachment.purge
          render json: { message: "Image deleted successfully (attachment in DB)" }
        else
          render json: { error: "Unauthorized access" }, status: :forbidden
        end

      else

        blob = ActiveStorage::Blob.find_by(key: params[:key])
        if blob.present?
          if blob.metadata["uploaded_by"]["organiser_id"] == current_user.organiser_id
            blob.purge
            render json: { message: "Image deleted successfully (blob only not in DB)" }
          else
            render json: { error: "Unauthorized access" }, status: :forbidden
          end
        else
          render json: { error: "Image not found" }, status: :not_found
        end
      end
    end
  end
end
