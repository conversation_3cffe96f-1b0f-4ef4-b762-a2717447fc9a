module Organisers
  class TicketTypesController < ApplicationController
    ALLOWED_INCLUDES = %w[ticket_type_promos].freeze
    before_action :set_ticket_type, only: %i[ show update destroy ]

    # GET /ticket_types
    def index
      if params[:event_id].present?
        event = Event.find(params[:event_id])
        if current_user.organiser && current_user.organiser.owns_event?(event)
          @ticket_types = TicketType.where(event_id: params[:event_id])
          render json: TicketTypeSerializer.new(@ticket_types, params: serializer_params).serializable_hash
        else
          render_api_error("Unauthorized access", :unprocessable_entity)
        end
      else
        render_api_error("event_id parameter is required", :unprocessable_entity)
      end
    end

    # GET /ticket_types/1
    def show
      render json: @ticket_type
    end

    # POST /ticket_types
    def create
      event = Event.find(ticket_type_params[:event_id])
      if current_user.organiser && current_user.organiser.owns_event?(event)
        @ticket_type = TicketType.new(ticket_type_params)

        if @ticket_type.save
          render json: @ticket_type, status: :created, location: @ticket_type
        else
          render_api_error(@ticket_type.errors.full_messages, :unprocessable_entity)
        end
      else
        render_api_error("Unauthorized access", :unprocessable_entity)
      end
    end

    # PATCH/PUT /ticket_types/1
    def update
      if @ticket_type.update(ticket_type_params)
        render json: @ticket_type
      else
        render_api_error(@ticket_type.errors.full_messages, :unprocessable_entity)
      end
    end

    # DELETE /ticket_types/1
    def destroy
      @ticket_type.destroy!
    end

    private
      def filter_and_set_includes(includes_param)
        # Split includes by comma and select only allowed associations
        requested_includes = includes_param.to_s.split(",").map(&:strip)
        @includes = requested_includes.select { |association| ALLOWED_INCLUDES.include?(association) }
      end

      # Use callbacks to share common setup or constraints between actions.
      def set_ticket_type
        @ticket_type = TicketType.find(params[:id])
        unless current_user.organiser && current_user.organiser.owns_ticket_type?(@ticket_type)
          render json: { error: "Unauthorized access" }, status: :unprocessable_entity
        end
      end

      # Only allow a list of trusted parameters through.
      def ticket_type_params
        params.require(:ticket_type).permit(*TICKET_TYPES_PARAMS)
      end
  end
end
