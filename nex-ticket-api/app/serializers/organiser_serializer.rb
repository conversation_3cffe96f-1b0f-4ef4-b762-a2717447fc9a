class OrganiserSerializer
  include JSONAPI::Serializer

  attributes :name, :contact_email, :state_id, :contact_mobile, :profile_picture

  attribute :profile_picture do |organiser|
    if organiser.profile_picture.attached?
      {
        url: Rails.application.routes.url_helpers.rails_blob_url(organiser.profile_picture, only_path: true),
        key: organiser.profile_picture.blob.key
      }
    end
  end

  attribute :vat_number, if: Proc.new { |record, params| params[:admin] || params[:organiser] }
  attribute :reg_number, if: Proc.new { |record, params| params[:admin] || params[:organiser] }
  attribute :default_currency, if: Proc.new { |record, params| params[:admin] || params[:organiser] }

  attribute :address do |organiser|
    organiser.state&.name
  end

  # has_many :events, if: Proc.new { |record, params| params[:admin] }
  # has_many :organiser_accounts, serializer: OrganiserAccountSerializer

  set_key_transform :camel_lower
end
