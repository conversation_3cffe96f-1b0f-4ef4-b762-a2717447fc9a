meta {
  name: SignUpCustomer
  type: http
  seq: 1
}

post {
  url: http://localhost:3000/api/signup
  body: json
  auth: none
}

headers {
  Accept: application/json
  Content-Type: application/json
}

body:json {
  {
    "user": {
      "email": "<EMAIL>",
      "password": "password",
      "first_name": "<PERSON><PERSON>",
      "last_name": "Zlia<PERSON>",
      "type": "Customer",
      "customer_attributes": {
        "mobile": "+421902902902"
      }
    }
  }
}

script:post-response {
  if(res.status === 200 ){
    bru.setVar("token", res.headers.authorization.replace('Bearer ', ''));
  }
  
}
