// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/ruby-rails-postgres
{
	"name": "Nex ticket API",
	"dockerComposeFile": "../docker-compose.yml",
	"service": "rails-api",
	"shutdownAction": "none",
	"workspaceFolder": "/workspaces/nex-ticket-api",
	"features": {
		"ghcr.io/nils-geistmann/devcontainers-features/zsh:0": {
			"theme": "robbyrussell",
			"plugins": "rails git ruby rbenv"
		},
		"ghcr.io/devcontainers/features/docker-outside-of-docker:1": {},
		"ghcr.io/robbert229/devcontainer-features/postgresql-client:1": {
			"version": 17
		}
	},
	"customizations": {
		"vscode": {
			"settings": {
				"terminal.integrated.defaultProfile.linux": "zsh",
				"remote.autoForwardPorts": false
			},
			"extensions": [
				"ms-azuretools.vscode-docker",
				"eamodio.gitlens",
				"mtxr.sqltools",
				"mtxr.sqltools-driver-pg",
				"bruno-api-client.bruno",
				"Gruntfuggly.todo-tree"
			]
		}
	}
	// Features to add to the dev container. More info: https://containers.dev/features.
	// "features": {},
	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// This can be used to network with other containers or the host.
	// "forwardPorts": [3000, 5432],
	// Use 'postCreateCommand' to run commands after the container is created.
	// "postCreateCommand": "bundle install && rake db:setup",
	// Configure tool-specific properties.
	// "customizations": {},
	// Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
	// "remoteUser": "root"
}