services:
  nuxt-frontend:
    container_name: nuxt_frontend
    build:
      context: ../nex-ticket-fe
      dockerfile: Dockerfile.dev
    restart: unless-stopped
    volumes:
      - ..:/workspaces:cached # Mount the parent directory into the container's /workspaces directory
    env_file:
      - ./.env.example
      - ./.env
    command: sh -c "/workspaces/nex-ticket-fe/startup.sh && exec sleep infinity"
    networks:
      - dockerNetwork

  rails-api:
    container_name: rails_api
    build:
      context: ../nex-ticket-api
      dockerfile: Dockerfile.dev
    volumes:
      - ..:/workspaces:cached
    restart: unless-stopped
    env_file:
      - ./.env.example
      - ./.env
    command: sh -c "/workspaces/nex-ticket-api/startup.sh; exec sleep infinity"
    depends_on:
      - postgres
    networks:
      - dockerNetwork

  postgres:
    image: postgres:latest
    restart: unless-stopped
    volumes:
      - nex-ticket-pgdata:/var/lib/postgresql/data
    env_file:
      - ./.env.example
      - ./.env
    networks:
      - dockerNetwork

  redis:
    image: redis:latest
    restart: unless-stopped
    networks:
      - dockerNetwork

  caddy:
    container_name: caddy
    image: caddy
    restart: unless-stopped
    cap_add:
      - NET_ADMIN
    volumes:
      - ../Caddyfile.dev:/etc/caddy/Caddyfile:z
    ports:
      - 3000:80
    networks:
      - dockerNetwork

  mailpit:
    image: axllent/mailpit
    container_name: mailpit
    restart: unless-stopped
    volumes:
      - mailpit_data:/data
    environment:
      MP_MAX_MESSAGES: 5000
      MP_DATABASE: /data/mailpit.db
      MP_SMTP_AUTH_ACCEPT_ANY: 1
      MP_SMTP_AUTH_ALLOW_INSECURE: 1
      MP_WEBROOT: /mailpit
    networks:
      - dockerNetwork

volumes:
  nex-ticket-pgdata:
  mailpit_data:


networks:
  dockerNetwork:
