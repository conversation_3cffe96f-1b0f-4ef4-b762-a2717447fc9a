// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/ruby-rails-postgres
{
	"name": "Nex ticket Frontend",
	"dockerComposeFile": "../docker-compose.yml",
	"service": "nuxt-frontend",
	"shutdownAction": "none",
	"workspaceFolder": "/workspaces/nex-ticket-fe",
	"features": {
		"ghcr.io/nils-geistmann/devcontainers-features/zsh:0": {
			"theme": "robbyrussell",
			"plugins": "git node"
		},
		"ghcr.io/devcontainers/features/docker-outside-of-docker:1": {},
		"ghcr.io/devcontainers-extra/features/pnpm:2": {}
	},
	"customizations": {
		"vscode": {
			"settings": {
				"terminal.integrated.defaultProfile.linux": "zsh",
				"remote.autoForwardPorts": false,
				"editor.defaultFormatter": "rvest.vs-code-prettier-eslint",
				"editor.formatOnType": false, // required
				"editor.formatOnPaste": false, // optional
				"editor.formatOnSave": true, // optional
				"editor.formatOnSaveMode": "file", // required to format on save
				"files.autoSave": "onFocusChange", // optional but recommended
				"vs-code-prettier-eslint.prettierLast": true // set as "true" to run 'prettier' last not first
			},
			"extensions": [
				"ms-azuretools.vscode-docker",
				"Gruntfuggly.todo-tree",
				"Nuxtr.nuxt-vscode-extentions",
				"stivo.tailwind-fold",
				"eamodio.gitlens",
				"bradlc.vscode-tailwindcss",
				"GitHub.copilot",
				"GitHub.copilot-chat",
				"rvest.vs-code-prettier-eslint",
			]
		}
	}
	// "postAttachCommand": "npm run dev"
	// Features to add to the dev container. More info: https://containers.dev/features.
	// "features": {},
	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// This can be used to network with other containers or the host.
	// "forwardPorts": [3000, 5432],
	// Use 'postCreateCommand' to run commands after the container is created.
	// "postCreateCommand": "bundle install && rake db:setup",
	// Configure tool-specific properties.
	// "customizations": {},
	// Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
	// "remoteUser": "root"
}